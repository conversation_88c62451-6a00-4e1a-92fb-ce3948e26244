/* AI Styled Image - Modern Admin Styles */

:root {
  --ai-admin-primary: #3b82f6;
  --ai-admin-primary-hover: #2563eb;
  --ai-admin-success: #10b981;
  --ai-admin-warning: #f59e0b;
  --ai-admin-danger: #ef4444;
  --ai-admin-background: #f8fafc;
  --ai-admin-surface: #ffffff;
  --ai-admin-text: #1e293b;
  --ai-admin-muted: #64748b;
  --ai-admin-border: #e2e8f0;
  --ai-admin-radius: 8px;
  --ai-admin-radius-sm: 6px;
  --ai-admin-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --ai-admin-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ai-admin-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-admin-dashboard {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--ai-admin-background);
  min-height: 100vh;
  line-height: 1.6;
}

.ai-admin-header {
  background: var(--ai-admin-surface);
  border-bottom: 1px solid var(--ai-admin-border);
  padding: 2rem 0;
  margin-bottom: 2rem;
}

.ai-header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.ai-brand {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.ai-logo {
  width: 48px;
  height: 48px;
  color: var(--ai-admin-primary);
}

.ai-brand h1 {
  font-size: 2rem;
  font-weight: 800;
  margin: 0;
  color: var(--ai-admin-text);
  letter-spacing: -0.025em;
}

.ai-brand p {
  font-size: 0.875rem;
  color: var(--ai-admin-muted);
  margin: 0;
}

.ai-stats-cards {
  display: flex;
  gap: 1.5rem;
}

.ai-stat-card {
  background: var(--ai-admin-surface);
  border: 1px solid var(--ai-admin-border);
  border-radius: var(--ai-admin-radius);
  padding: 1.5rem;
  text-align: center;
  min-width: 120px;
  box-shadow: var(--ai-admin-shadow);
}

.ai-stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--ai-admin-primary);
  line-height: 1;
  margin-bottom: 0.5rem;
  display: block;
}

.ai-stat-label {
  font-size: 0.875rem;
  color: var(--ai-admin-muted);
  font-weight: 500;
}

.ai-status-active {
  color: var(--ai-admin-success) !important;
}

.ai-status-inactive {
  color: var(--ai-admin-danger) !important;
}

.ai-admin-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem 4rem;
}

.ai-admin-grid {
  display: grid;
  gap: 2rem;
}

.ai-admin-section {
  background: var(--ai-admin-surface);
  border-radius: var(--ai-admin-radius);
  border: 1px solid var(--ai-admin-border);
  overflow: hidden;
  box-shadow: var(--ai-admin-shadow);
}

.ai-section-header {
  padding: 2rem;
  border-bottom: 1px solid var(--ai-admin-border);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.ai-section-title h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: var(--ai-admin-text);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.ai-section-title h2 svg {
  width: 24px;
  height: 24px;
  color: var(--ai-admin-primary);
}

.ai-section-title p {
  color: var(--ai-admin-muted);
  margin: 0;
  font-size: 0.875rem;
}

.ai-section-actions {
  display: flex;
  gap: 1rem;
  flex-shrink: 0;
}

.ai-overlay-management .ai-section-header {
  background: var(--ai-admin-background);
}

.ai-upload-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.ai-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(4px);
}

.ai-modal-container {
  position: relative;
  background: var(--ai-admin-surface);
  border-radius: var(--ai-admin-radius);
  box-shadow: var(--ai-admin-shadow-lg);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.ai-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid var(--ai-admin-border);
}

.ai-modal-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--ai-admin-text);
}

.ai-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: var(--ai-admin-muted);
  border-radius: var(--ai-admin-radius-sm);
  transition: var(--ai-admin-transition);
}

.ai-close-btn:hover {
  background: var(--ai-admin-background);
  color: var(--ai-admin-text);
}

.ai-close-btn svg {
  width: 20px;
  height: 20px;
}

.ai-modal-body {
  padding: 2rem;
}

.ai-upload-dropzone {
  border: 2px dashed var(--ai-admin-border);
  border-radius: var(--ai-admin-radius);
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: var(--ai-admin-transition);
  margin-bottom: 2rem;
  background: var(--ai-admin-background);
}

.ai-upload-dropzone:hover,
.ai-upload-dropzone.drag-over {
  border-color: var(--ai-admin-primary);
  background: color-mix(in srgb, var(--ai-admin-primary) 5%, var(--ai-admin-surface));
}

.ai-upload-icon svg {
  width: 48px;
  height: 48px;
  color: var(--ai-admin-primary);
  margin-bottom: 1rem;
}

.ai-upload-dropzone h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--ai-admin-text);
}

.ai-upload-dropzone p {
  color: var(--ai-admin-muted);
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
}

.ai-upload-specs {
  font-size: 0.75rem;
  color: var(--ai-admin-muted);
  background: var(--ai-admin-surface);
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  display: inline-block;
}

.ai-image-preview {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background: var(--ai-admin-background);
  border-radius: var(--ai-admin-radius);
  margin-bottom: 2rem;
  border: 1px solid var(--ai-admin-border);
}

.ai-preview-image {
  flex-shrink: 0;
}

.ai-preview-image img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: var(--ai-admin-radius-sm);
  border: 2px solid var(--ai-admin-surface);
  box-shadow: var(--ai-admin-shadow);
}

.ai-preview-info h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--ai-admin-text);
}

.ai-preview-info p {
  font-size: 0.875rem;
  color: var(--ai-admin-muted);
  margin: 0 0 1rem 0;
}

.ai-settings-form,
.ai-overlay-details {
  padding: 2rem;
}

.ai-form-grid,
.ai-form-row {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.ai-form-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.ai-form-row {
  grid-template-columns: 1fr 1fr;
}

.ai-form-group {
  display: flex;
  flex-direction: column;
}

.ai-form-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--ai-admin-text);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.ai-required {
  color: var(--ai-admin-danger);
  font-weight: 700;
}

.ai-optional {
  color: var(--ai-admin-muted);
  font-weight: 400;
  font-size: 0.8125rem;
}

.ai-input-field {
  padding: 0.75rem 1rem;
  border: 1px solid var(--ai-admin-border);
  border-radius: var(--ai-admin-radius-sm);
  font-size: 0.875rem;
  font-family: inherit;
  transition: var(--ai-admin-transition);
  background: var(--ai-admin-surface);
}

.ai-input-field:focus {
  outline: none;
  border-color: var(--ai-admin-primary);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.ai-input-field[type="password"] {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  letter-spacing: 0.05em;
}

.ai-input-wrapper {
  position: relative;
}

.ai-toggle-password {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--ai-admin-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--ai-admin-radius-sm);
  transition: var(--ai-admin-transition);
}

.ai-toggle-password:hover {
  color: var(--ai-admin-text);
  background: var(--ai-admin-background);
}

.ai-toggle-password svg {
  width: 16px;
  height: 16px;
}

.ai-field-help {
  font-size: 0.75rem;
  color: var(--ai-admin-muted);
  margin-top: 0.25rem;
  line-height: 1.4;
}

.ai-field-help a {
  color: var(--ai-admin-primary);
  text-decoration: none;
  font-weight: 500;
}

.ai-field-help a:hover {
  text-decoration: underline;
}

.ai-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--ai-admin-radius-sm);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--ai-admin-transition);
  border: none;
  text-decoration: none;
  line-height: 1;
}

.ai-btn svg {
  width: 16px;
  height: 16px;
}

.ai-btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.8125rem;
}

.ai-btn-small svg {
  width: 14px;
  height: 14px;
}

.ai-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.ai-btn-primary {
  background: var(--ai-admin-primary);
  color: var(--ai-admin-surface);
  box-shadow: var(--ai-admin-shadow);
}

.ai-btn-primary:hover:not(:disabled) {
  background: var(--ai-admin-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--ai-admin-shadow-lg);
}

.ai-btn-secondary {
  background: var(--ai-admin-surface);
  color: var(--ai-admin-text);
  border: 1px solid var(--ai-admin-border);
}

.ai-btn-secondary:hover {
  background: var(--ai-admin-background);
}

.ai-identity-form {
  padding: 2rem;
}

.ai-identity-sections {
  display: grid;
  gap: 2rem;
  margin-bottom: 2rem;
}

.ai-identity-section {
  border: 1px solid var(--ai-admin-border);
  border-radius: var(--ai-admin-radius);
  padding: 1.5rem;
  background: var(--ai-admin-background);
}

.ai-identity-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--ai-admin-text);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ai-colors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.ai-color-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ai-color-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ai-admin-text);
}

.ai-color-input-wrapper {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid var(--ai-admin-border);
  border-radius: var(--ai-admin-radius-sm);
  background: var(--ai-admin-surface);
}

.ai-color-input {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--ai-admin-radius-sm);
  cursor: pointer;
  background: none;
}

.ai-color-input::-webkit-color-swatch-wrapper {
  padding: 0;
}

.ai-color-input::-webkit-color-swatch {
  border: 2px solid var(--ai-admin-border);
  border-radius: var(--ai-admin-radius-sm);
}

.ai-color-value {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-size: 0.875rem;
  color: var(--ai-admin-text);
  font-weight: 500;
  flex: 1;
}

.ai-form-actions,
.ai-modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 2rem;
  border-top: 1px solid var(--ai-admin-border);
  background: var(--ai-admin-background);
}

.ai-overlays-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

.ai-overlay-card {
  background: var(--ai-admin-surface);
  border: 1px solid var(--ai-admin-border);
  border-radius: var(--ai-admin-radius);
  overflow: hidden;
  transition: var(--ai-admin-transition);
  position: relative;
}

.ai-overlay-card:hover {
  box-shadow: var(--ai-admin-shadow-lg);
  transform: translateY(-2px);
}

.ai-overlay-image {
  position: relative;
  background: var(--ai-admin-background);
  height: 200px;
  overflow: hidden;
}

.ai-overlay-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.ai-overlay-card:hover .ai-overlay-image img {
  transform: scale(1.05);
}

.ai-overlay-actions {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transition: var(--ai-admin-transition);
}

.ai-overlay-card:hover .ai-overlay-actions {
  opacity: 1;
}

.ai-action-btn {
  background: var(--ai-admin-surface);
  border: 1px solid var(--ai-admin-border);
  border-radius: var(--ai-admin-radius-sm);
  padding: 0.5rem;
  cursor: pointer;
  transition: var(--ai-admin-transition);
  color: var(--ai-admin-text);
  box-shadow: var(--ai-admin-shadow);
}

.ai-action-btn:hover {
  background: var(--ai-admin-background);
  transform: translateY(-1px);
}

.ai-action-btn svg {
  width: 14px;
  height: 14px;
}

.ai-edit-overlay svg {
  color: var(--ai-admin-primary);
}

.ai-delete-overlay svg {
  color: var(--ai-admin-danger);
}

.ai-overlay-info {
  padding: 1rem;
}

.ai-overlay-info h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--ai-admin-text);
}

.ai-overlay-description {
  font-size: 0.875rem;
  color: var(--ai-admin-muted);
  margin: 0 0 1rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.ai-overlay-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.ai-category {
  background: var(--ai-admin-background);
  color: var(--ai-admin-text);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.ai-usage {
  color: var(--ai-admin-muted);
  font-weight: 500;
}

.ai-empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--ai-admin-muted);
}

.ai-empty-icon {
  margin-bottom: 1rem;
}

.ai-empty-icon svg {
  width: 64px;
  height: 64px;
  color: var(--ai-admin-muted);
}

.ai-empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--ai-admin-text);
}

.ai-empty-state p {
  margin: 0 0 2rem 0;
}

.ai-empty-action {
  margin-top: 1rem;
}

.ai-guide-content {
  padding: 2rem;
}

.ai-guide-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.ai-guide-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--ai-admin-background);
  border-radius: var(--ai-admin-radius);
  border: 1px solid var(--ai-admin-border);
}

.ai-step-number {
  background: var(--ai-admin-primary);
  color: var(--ai-admin-surface);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.ai-step-content h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--ai-admin-text);
}

.ai-step-content p {
  font-size: 0.875rem;
  color: var(--ai-admin-muted);
  margin: 0;
  line-height: 1.4;
}

.ai-step-content code {
  background: var(--ai-admin-surface);
  color: var(--ai-admin-primary);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-size: 0.8125rem;
  font-weight: 500;
}

.notice {
  background: var(--ai-admin-surface);
  border: 1px solid var(--ai-admin-border);
  border-radius: var(--ai-admin-radius);
  padding: 1rem;
  margin: 1rem 0;
  box-shadow: var(--ai-admin-shadow);
}

.notice-success {
  background: color-mix(in srgb, var(--ai-admin-success) 10%, var(--ai-admin-surface));
  border-color: var(--ai-admin-success);
  color: var(--ai-admin-success);
}

.notice-error {
  background: color-mix(in srgb, var(--ai-admin-danger) 10%, var(--ai-admin-surface));
  border-color: var(--ai-admin-danger);
  color: var(--ai-admin-danger);
}

.notice-warning {
  background: color-mix(in srgb, var(--ai-admin-warning) 10%, var(--ai-admin-surface));
  border-color: var(--ai-admin-warning);
  color: var(--ai-admin-warning);
}

.notice p {
  margin: 0;
  font-weight: 500;
}

.ai-btn.loading {
  opacity: 0.7;
  pointer-events: none;
}

.ai-btn.loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 1200px) {
  .ai-header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }

  .ai-stats-cards {
    justify-content: center;
  }

  .ai-section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .ai-header-content,
  .ai-admin-content {
    padding: 0 1rem;
  }

  .ai-stats-cards {
    flex-direction: column;
    gap: 1rem;
  }

  .ai-form-grid,
  .ai-form-row {
    grid-template-columns: 1fr;
  }

  .ai-overlays-grid {
    grid-template-columns: 1fr;
  }

  .ai-guide-steps {
    grid-template-columns: 1fr;
  }

  .ai-modal-container {
    margin: 1rem;
  }

  .ai-modal-header,
  .ai-modal-body {
    padding: 1.5rem;
  }

  .ai-form-actions,
  .ai-modal-actions {
    flex-direction: column;
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .ai-brand h1 {
    font-size: 1.5rem;
  }

  .ai-stats-cards {
    gap: 0.75rem;
  }

  .ai-stat-card {
    padding: 1rem;
  }

  .ai-guide-step {
    flex-direction: column;
    text-align: center;
  }

  .ai-image-preview {
    flex-direction: column;
    text-align: center;
  }
}

.ai-btn:focus,
.ai-input-field:focus,
.ai-color-input:focus {
  outline: 2px solid var(--ai-admin-primary);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (max-width: 768px) {
  .ai-colors-grid {
    grid-template-columns: 1fr;
  }

  .ai-color-input-wrapper {
    justify-content: space-between;
  }

  .ai-color-input {
    width: 32px;
    height: 32px;
  }
} 