<?php
/**
 * Admin Dashboard - Modern Single Page Interface
 */

defined('ABSPATH') || exit;

require_once AI_STYLED_PATH . 'includes/processor.php';
$processor = new AI_Image_Processor();

if ($_POST) {
    if (isset($_POST['save_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'ai_styled_settings')) {
        $settings = [
            'api_token' => sanitize_text_field($_POST['api_token'] ?? ''),
            'model_endpoint' => sanitize_text_field($_POST['model_endpoint'] ?? 'flux-kontext-apps/multi-image-kontext-pro'),
            'max_file_size' => absint($_POST['max_file_size'] ?? 10485760),
            'rate_limit' => absint($_POST['rate_limit'] ?? 50),
            'allowed_formats' => array_map('sanitize_text_field', $_POST['allowed_formats'] ?? ['jpg', 'jpeg', 'png', 'webp']),
            'openrouter_api_key' => sanitize_text_field($_POST['openrouter_api_key'] ?? ''),
            'openrouter_model' => sanitize_text_field($_POST['openrouter_model'] ?? 'openai/gpt-4.1'),
            'processing_mode' => sanitize_text_field($_POST['processing_mode'] ?? 'new')
        ];

        foreach ($settings as $key => $value) {
            update_option("ai_styled_{$key}", $value);
        }

        echo '<div class="notice notice-success is-dismissible"><p>Settings saved successfully!</p></div>';
    }
}

$settings = [
    'api_token' => get_option('ai_styled_api_token', ''),
    'model_endpoint' => get_option('ai_styled_model_endpoint', 'flux-kontext-apps/multi-image-kontext-pro'),
    'max_file_size' => get_option('ai_styled_max_file_size', 10485760),
    'rate_limit' => get_option('ai_styled_rate_limit', 50),
    'allowed_formats' => get_option('ai_styled_allowed_formats', ['jpg', 'jpeg', 'png', 'webp']),
    'openrouter_api_key' => get_option('ai_styled_openrouter_api_key', ''),
    'openrouter_model' => get_option('ai_styled_openrouter_model', 'openai/gpt-4.1'),
    'processing_mode' => get_option('ai_styled_processing_mode', 'new')
];

$identity_settings = AIStyledImagePlugin::instance()->get_identity_settings();
$overlays = $processor->get_overlays();
global $wpdb;
$total_overlays = count($overlays);
$total_usage = array_sum(wp_list_pluck($overlays, 'usage_count'));
?>

<div class="ai-admin-dashboard">
    <div class="ai-admin-header">
        <div class="ai-header-content">
            <div class="ai-brand">
                <svg class="ai-logo" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                    <circle cx="8.5" cy="8.5" r="1.5"/>
                    <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                </svg>
                <div>
                    <h1>AI Styled Image</h1>
                    <p>Modern architectural visualization tool</p>
                </div>
            </div>
            <div class="ai-stats-cards">
                <div class="ai-stat-card">
                    <div class="ai-stat-number"><?php echo $total_overlays; ?></div>
                    <div class="ai-stat-label">Overlays</div>
                </div>
                <div class="ai-stat-card">
                    <div class="ai-stat-number"><?php echo $total_usage; ?></div>
                    <div class="ai-stat-label">Generated</div>
                </div>
                <div class="ai-stat-card">
                    <div class="ai-stat-number <?php echo !empty($settings['api_token']) ? 'ai-status-active' : 'ai-status-inactive'; ?>">
                        <?php echo !empty($settings['api_token']) ? 'Active' : 'Inactive'; ?>
                    </div>
                    <div class="ai-stat-label">Replicate API</div>
                </div>
                <div class="ai-stat-card">
                    <div class="ai-stat-number <?php echo !empty($settings['openrouter_api_key']) ? 'ai-status-active' : 'ai-status-inactive'; ?>">
                        <?php echo !empty($settings['openrouter_api_key']) ? 'Active' : 'Inactive'; ?>
                    </div>
                    <div class="ai-stat-label">OpenRouter API</div>
                </div>
            </div>
        </div>
    </div>

    <div class="ai-admin-content">
        <div class="ai-admin-grid">
            
            <section class="ai-admin-section ai-overlay-management">
                <div class="ai-section-header">
                    <div class="ai-section-title">
                        <h2>
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <circle cx="8.5" cy="8.5" r="1.5"/>
                                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                            </svg>
                            Overlay Library
                        </h2>
                        <p>Manage your architectural overlay images</p>
                    </div>
                    <div class="ai-section-actions">
                        <button type="button" id="add-overlay-btn" class="ai-btn ai-btn-primary">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 5v14M5 12h14"/>
                            </svg>
                            Add New Overlay
                        </button>
                        <button type="button" id="media-library-btn" class="ai-btn ai-btn-secondary">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <path d="m9 9 5 12 1.774-3.326a.5.5 0 0 1 .852-.174l2.853 3.5"/>
                                <path d="m9 9 5 12 1.774-3.326a.5.5 0 0 1 .852-.174l2.853 3.5"/>
                                <circle cx="7.5" cy="7.5" r="1.5"/>
                            </svg>
                            From Media Library
                        </button>
                    </div>
                </div>
                
                <div id="upload-modal" class="ai-upload-modal" style="display: none;">
                    <div class="ai-modal-overlay"></div>
                    <div class="ai-modal-container">
                        <div class="ai-modal-header">
                            <h3>Add New Overlay</h3>
                            <button type="button" id="close-upload-modal" class="ai-close-btn">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18"/>
                                    <line x1="6" y1="6" x2="18" y2="18"/>
                                </svg>
                            </button>
                        </div>
                        
                        <div class="ai-modal-body">
                            <form id="overlay-upload-form" enctype="multipart/form-data">
                                <div class="ai-upload-methods">
                                    <div class="ai-upload-method" id="file-upload-method">
                                        <div class="ai-upload-dropzone" id="upload-dropzone">
                                            <div class="ai-upload-icon">
                                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                                    <polyline points="7,10 12,15 17,10"/>
                                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                                </svg>
                                            </div>
                                            <h4>Drop PNG file here</h4>
                                            <p>or click to browse files</p>
                                            <div class="ai-upload-specs">
                                                Transparent PNG only • Max <?php echo size_format($settings['max_file_size']); ?>
                                            </div>
                                        </div>
                                        <input type="file" id="overlay-file" name="overlay_image" accept="image/png" style="display: none;">
                                        <input type="hidden" id="media-attachment-id" name="media_id" value="">
                                    </div>
                                </div>
                                
                                <div id="image-preview-area" class="ai-image-preview" style="display: none;">
                                    <div class="ai-preview-image">
                                        <img id="preview-img" src="" alt="Preview">
                                    </div>
                                    <div class="ai-preview-info">
                                        <h4 id="preview-name"></h4>
                                        <p id="preview-size"></p>
                                        <button type="button" id="change-image" class="ai-btn ai-btn-small">Change Image</button>
                                    </div>
                                </div>
                                
                                <div class="ai-overlay-details">
                                    <div class="ai-form-row">
                                        <div class="ai-form-group">
                                            <label for="overlay-title">
                                                <strong>Overlay Name</strong>
                                                <span class="ai-required">*</span>
                                            </label>
                                            <input type="text" id="overlay-title" name="title" required class="ai-input-field" placeholder="e.g., Modern Glass Room">
                                        </div>
                                        
                                        <div class="ai-form-group">
                                            <label for="overlay-category">
                                                <strong>Category</strong>
                                            </label>
                                            <select id="overlay-category" name="category" class="ai-input-field">
                                                <option value="Glass Room">Glass Room</option>
                                                <option value="Veranda">Veranda</option>
                                                <option value="Shading System">Shading System</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="ai-form-group">
                                        <label for="overlay-description">
                                            <strong>Description</strong>
                                        </label>
                                        <textarea id="overlay-description" name="description" rows="3" class="ai-input-field" placeholder="Describe this architectural element..."></textarea>
                                    </div>
                                    
                                    <div class="ai-form-group">
                                        <label for="overlay-prompt">
                                            <strong>Custom AI Prompt</strong>
                                            <span class="ai-optional">(Optional)</span>
                                        </label>
                                        <textarea id="overlay-prompt" name="prompt_template" rows="4" class="ai-input-field" placeholder="Enter a custom prompt for this overlay, or leave empty for automatic generation..."></textarea>
                                        <p class="ai-field-help">This prompt will be used when users select this overlay. Leave empty to use the default prompt for this category.</p>
                                    </div>
                                </div>
                                
                                <div class="ai-modal-actions">
                                    <button type="button" id="cancel-upload" class="ai-btn ai-btn-secondary">Cancel</button>
                                    <button type="submit" id="upload-overlay-btn" class="ai-btn ai-btn-primary">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                            <polyline points="17,8 12,3 7,8"/>
                                            <line x1="12" y1="3" x2="12" y2="15"/>
                                        </svg>
                                        Upload Overlay
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="ai-overlays-grid">
                    <?php if (empty($overlays)): ?>
                        <div class="ai-empty-state">
                            <div class="ai-empty-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                    <circle cx="8.5" cy="8.5" r="1.5"/>
                                    <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                                </svg>
                            </div>
                            <h3>No overlays yet</h3>
                            <p>Upload your first architectural overlay to get started</p>
                            <button type="button" class="ai-btn ai-btn-primary ai-empty-action">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 5v14M5 12h14"/>
                                </svg>
                                Add Your First Overlay
                            </button>
                        </div>
                    <?php else: ?>
                        <?php foreach ($overlays as $overlay): ?>
                            <div class="ai-overlay-card" data-id="<?php echo esc_attr($overlay->id); ?>">
                                <div class="ai-overlay-image">
                                    <img src="<?php echo esc_url($overlay->image_url); ?>" alt="<?php echo esc_attr($overlay->title); ?>">
                                    <div class="ai-overlay-actions">
                                        <button type="button" class="ai-action-btn ai-edit-overlay" data-id="<?php echo esc_attr($overlay->id); ?>" title="Edit">
                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                <path d="m18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                            </svg>
                                        </button>
                                        <button type="button" class="ai-action-btn ai-delete-overlay" data-id="<?php echo esc_attr($overlay->id); ?>" title="Delete">
                                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <polyline points="3,6 5,6 21,6"/>
                                                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="ai-overlay-info">
                                    <h3><?php echo esc_html($overlay->title); ?></h3>
                                    <p class="ai-overlay-description"><?php echo esc_html($overlay->description); ?></p>
                                    <div class="ai-overlay-meta">
                                        <span class="ai-category"><?php echo esc_html(ucfirst(str_replace('_', ' ', $overlay->category))); ?></span>
                                        <span class="ai-usage">Used <?php echo intval($overlay->usage_count); ?> times</span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </section>

            <section class="ai-admin-section">
                <div class="ai-section-header">
                    <div class="ai-section-title">
                        <h2>
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="m12 1 1.09 1.09L15 3l1.09 1.09L17 6l-1.09 1.09L15 9l-1.09-1.09L12 6 10.91 7.09 9 9l1.09 1.09L12 12"/>
                            </svg>
                            API Configuration
                        </h2>
                        <p>Configure your Replicate API settings for AI processing</p>
                    </div>
                </div>
                
                <form method="post" class="ai-settings-form">
                    <?php wp_nonce_field('ai_styled_settings'); ?>
                    
                    <div class="ai-form-grid">
                        <div class="ai-form-group">
                            <label for="api_token">
                                <strong>Replicate API Token</strong>
                                <span class="ai-required">*</span>
                            </label>
                            <div class="ai-input-wrapper">
                                <input 
                                    type="password" 
                                    id="api_token" 
                                    name="api_token" 
                                    value="<?php echo esc_attr($settings['api_token']); ?>"
                                    placeholder="r8_..."
                                    class="ai-input-field"
                                >
                                <button type="button" class="ai-toggle-password" data-target="api_token">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                        <circle cx="12" cy="12" r="3"/>
                                    </svg>
                                </button>
                            </div>
                            <p class="ai-field-help">
                                Get your API token from <a href="https://replicate.com/account/api-tokens" target="_blank">Replicate</a>
                            </p>
                        </div>
                        
                        <div class="ai-form-group">
                            <label for="model_endpoint">
                                <strong>Model Endpoint</strong>
                            </label>
                            <input 
                                type="text" 
                                id="model_endpoint" 
                                name="model_endpoint" 
                                value="<?php echo esc_attr($settings['model_endpoint']); ?>"
                                class="ai-input-field"
                            >
                            <p class="ai-field-help">Default model for image processing</p>
                        </div>
                        
                        <div class="ai-form-group">
                            <label for="max_file_size">
                                <strong>Max File Size</strong>
                            </label>
                            <select id="max_file_size" name="max_file_size" class="ai-input-field">
                                <option value="5242880" <?php selected($settings['max_file_size'], 5242880); ?>>5 MB</option>
                                <option value="10485760" <?php selected($settings['max_file_size'], 10485760); ?>>10 MB</option>
                                <option value="15728640" <?php selected($settings['max_file_size'], 15728640); ?>>15 MB</option>
                                <option value="20971520" <?php selected($settings['max_file_size'], 20971520); ?>>20 MB</option>
                            </select>
                            <p class="ai-field-help">Maximum upload size for user images</p>
                        </div>
                        
                        <div class="ai-form-group">
                            <label for="rate_limit">
                                <strong>Hourly Rate Limit</strong>
                            </label>
                            <input 
                                type="number" 
                                id="rate_limit" 
                                name="rate_limit" 
                                value="<?php echo esc_attr($settings['rate_limit']); ?>"
                                min="1"
                                max="1000"
                                class="ai-input-field"
                            >
                            <p class="ai-field-help">Maximum requests per user per hour</p>
                        </div>
                    </div>
                    
                    <div class="ai-form-group">
                        <label for="processing_mode">
                            <strong>Processing Mode</strong>
                        </label>
                        <select id="processing_mode" name="processing_mode" class="ai-input-field">
                            <option value="current" <?php selected($settings['processing_mode'], 'current'); ?>>Current Mode - Manual Prompts</option>
                            <option value="new" <?php selected($settings['processing_mode'], 'new'); ?>>New Mode - AI-Generated Prompts</option>
                        </select>
                        <p class="ai-field-help">Choose between manual prompts (current) or AI-generated prompts using OpenRouter (new)</p>
                    </div>

                    <div class="ai-form-actions">
                        <button type="submit" name="save_settings" class="ai-btn ai-btn-primary">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="m9 12 2 2 4-4"/>
                                <path d="M21 12c.552 0 1-.448 1-1V5a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v6c0 .552.448 1 1 1h18z"/>
                            </svg>
                            Save Settings
                        </button>
                    </div>
                </form>
            </section>

            <section class="ai-admin-section">
                <div class="ai-section-header">
                    <div class="ai-section-title">
                        <h2>
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 12l2 2 4-4"/>
                                <path d="M21 12c.552 0 1-.448 1-1V5a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v6c0 .552.448 1 1 1h18z"/>
                            </svg>
                            OpenRouter Configuration
                        </h2>
                        <p>Configure OpenRouter API for AI-powered prompt generation (New Mode only)</p>
                    </div>
                </div>

                <form method="post" class="ai-settings-form">
                    <?php wp_nonce_field('ai_styled_settings'); ?>

                    <div class="ai-form-grid">
                        <div class="ai-form-group">
                            <label for="openrouter_api_key">
                                <strong>OpenRouter API Key</strong>
                                <span class="ai-required">*</span>
                            </label>
                            <div class="ai-input-wrapper">
                                <input
                                    type="password"
                                    id="openrouter_api_key"
                                    name="openrouter_api_key"
                                    value="<?php echo esc_attr($settings['openrouter_api_key']); ?>"
                                    placeholder="sk-or-..."
                                    class="ai-input-field"
                                >
                                <button type="button" class="ai-toggle-password" data-target="openrouter_api_key">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                        <circle cx="12" cy="12" r="3"/>
                                    </svg>
                                </button>
                            </div>
                            <p class="ai-field-help">
                                Get your API key from <a href="https://openrouter.ai/keys" target="_blank">OpenRouter</a>
                            </p>
                        </div>

                        <div class="ai-form-group">
                            <label for="openrouter_model">
                                <strong>GPT Vision Model</strong>
                            </label>
                            <select id="openrouter_model" name="openrouter_model" class="ai-input-field">
                                <option value="openai/gpt-4.1" <?php selected($settings['openrouter_model'], 'openai/gpt-4.1'); ?>>GPT-4.1</option>
                                <option value="openai/gpt-4.1-mini" <?php selected($settings['openrouter_model'], 'openai/gpt-4.1-mini'); ?>>GPT-4.1 Mini</option>
                                <option value="openai/gpt-4.1-nano" <?php selected($settings['openrouter_model'], 'openai/gpt-4.1-nano'); ?>>GPT-4.1 Nano</option>
                            </select>
                            <p class="ai-field-help">Choose the GPT model for analyzing images and generating prompts</p>
                        </div>
                    </div>

                    <div class="ai-form-actions">
                        <button type="submit" name="save_settings" class="ai-btn ai-btn-primary">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="m9 12 2 2 4-4"/>
                                <path d="M21 12c.552 0 1-.448 1-1V5a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v6c0 .552.448 1 1 1h18z"/>
                            </svg>
                            Save OpenRouter Settings
                        </button>
                    </div>
                </form>
            </section>

            <section class="ai-admin-section">
                <div class="ai-section-header">
                    <div class="ai-section-title">
                        <h2>
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            Brand Identity & Colors
                        </h2>
                        <p>Customize the appearance and branding of your AI image tool</p>
                    </div>
                </div>
                
                <form id="identity-form" class="ai-identity-form">
                    <div class="ai-identity-sections">
                        <div class="ai-identity-section">
                            <h3>Brand Settings</h3>
                            <div class="ai-form-row">
                                <div class="ai-form-group">
                                    <label for="brand-name">Brand Name</label>
                                    <input type="text" 
                                           id="brand-name" 
                                           name="brand_name" 
                                           value="<?php echo esc_attr($identity_settings['brand_name']); ?>" 
                                           class="ai-input-field" 
                                           placeholder="AI STYLED"
                                           required>
                                </div>
                                <div class="ai-form-group">
                                    <label for="font-family">Font Family</label>
                                    <select id="font-family" name="font_family" class="ai-input-field">
                                        <option value="Inter" <?php selected($identity_settings['font_family'], 'Inter'); ?>>Inter</option>
                                        <option value="Roboto" <?php selected($identity_settings['font_family'], 'Roboto'); ?>>Roboto</option>
                                        <option value="Open Sans" <?php selected($identity_settings['font_family'], 'Open Sans'); ?>>Open Sans</option>
                                        <option value="Poppins" <?php selected($identity_settings['font_family'], 'Poppins'); ?>>Poppins</option>
                                        <option value="Montserrat" <?php selected($identity_settings['font_family'], 'Montserrat'); ?>>Montserrat</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="ai-identity-section">
                            <h3>Color Scheme</h3>
                            <div class="ai-colors-grid">
                                <div class="ai-color-group">
                                    <label for="primary-color">Primary Color</label>
                                    <div class="ai-color-input-wrapper">
                                        <input type="color" 
                                               id="primary-color" 
                                               name="primary_color" 
                                               value="<?php echo esc_attr($identity_settings['primary_color']); ?>" 
                                               class="ai-color-input">
                                        <span class="ai-color-value"><?php echo esc_html($identity_settings['primary_color']); ?></span>
                                    </div>
                                </div>
                                
                                <div class="ai-color-group">
                                    <label for="secondary-color">Secondary Color</label>
                                    <div class="ai-color-input-wrapper">
                                        <input type="color" 
                                               id="secondary-color" 
                                               name="secondary_color" 
                                               value="<?php echo esc_attr($identity_settings['secondary_color']); ?>" 
                                               class="ai-color-input">
                                        <span class="ai-color-value"><?php echo esc_html($identity_settings['secondary_color']); ?></span>
                                    </div>
                                </div>
                                
                                <div class="ai-color-group">
                                    <label for="accent-color">Accent Color</label>
                                    <div class="ai-color-input-wrapper">
                                        <input type="color" 
                                               id="accent-color" 
                                               name="accent_color" 
                                               value="<?php echo esc_attr($identity_settings['accent_color']); ?>" 
                                               class="ai-color-input">
                                        <span class="ai-color-value"><?php echo esc_html($identity_settings['accent_color']); ?></span>
                                    </div>
                                </div>
                                
                                <div class="ai-color-group">
                                    <label for="background-color">Background Color</label>
                                    <div class="ai-color-input-wrapper">
                                        <input type="color" 
                                               id="background-color" 
                                               name="background_color" 
                                               value="<?php echo esc_attr($identity_settings['background_color']); ?>" 
                                               class="ai-color-input">
                                        <span class="ai-color-value"><?php echo esc_html($identity_settings['background_color']); ?></span>
                                    </div>
                                </div>
                                
                                <div class="ai-color-group">
                                    <label for="surface-color">Surface Color</label>
                                    <div class="ai-color-input-wrapper">
                                        <input type="color" 
                                               id="surface-color" 
                                               name="surface_color" 
                                               value="<?php echo esc_attr($identity_settings['surface_color']); ?>" 
                                               class="ai-color-input">
                                        <span class="ai-color-value"><?php echo esc_html($identity_settings['surface_color']); ?></span>
                                    </div>
                                </div>
                                
                                <div class="ai-color-group">
                                    <label for="text-color">Text Color</label>
                                    <div class="ai-color-input-wrapper">
                                        <input type="color" 
                                               id="text-color" 
                                               name="text_color" 
                                               value="<?php echo esc_attr($identity_settings['text_color']); ?>" 
                                               class="ai-color-input">
                                        <span class="ai-color-value"><?php echo esc_html($identity_settings['text_color']); ?></span>
                                    </div>
                                </div>
                                
                                <div class="ai-color-group">
                                    <label for="muted-color">Muted Text Color</label>
                                    <div class="ai-color-input-wrapper">
                                        <input type="color" 
                                               id="muted-color" 
                                               name="muted_color" 
                                               value="<?php echo esc_attr($identity_settings['muted_color']); ?>" 
                                               class="ai-color-input">
                                        <span class="ai-color-value"><?php echo esc_html($identity_settings['muted_color']); ?></span>
                                    </div>
                                </div>
                                
                                <div class="ai-color-group">
                                    <label for="border-color">Border Color</label>
                                    <div class="ai-color-input-wrapper">
                                        <input type="color" 
                                               id="border-color" 
                                               name="border_color" 
                                               value="<?php echo esc_attr($identity_settings['border_color']); ?>" 
                                               class="ai-color-input">
                                        <span class="ai-color-value"><?php echo esc_html($identity_settings['border_color']); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="ai-identity-section">
                            <h3>Style Settings</h3>
                            <div class="ai-form-row">
                                <div class="ai-form-group">
                                    <label for="border-radius">Border Radius (px)</label>
                                    <input type="number" 
                                           id="border-radius" 
                                           name="border_radius" 
                                           value="<?php echo esc_attr($identity_settings['border_radius']); ?>" 
                                           class="ai-input-field" 
                                           min="0"
                                           max="50"
                                           placeholder="8">
                                </div>
                                <div class="ai-form-group">
                                    <label for="form-style">Form Style</label>
                                    <select id="form-style" name="form_style" class="ai-input-field">
                                        <option value="modern" <?php selected($identity_settings['form_style'], 'modern'); ?>>Modern</option>
                                        <option value="minimal" <?php selected($identity_settings['form_style'], 'minimal'); ?>>Minimal</option>
                                        <option value="classic" <?php selected($identity_settings['form_style'], 'classic'); ?>>Classic</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ai-form-actions">
                        <button type="submit" class="ai-btn ai-btn-primary">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="m9 12 2 2 4-4"/>
                                <path d="M21 12c.552 0 1-.448 1-1V5a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v6c0 .552.448 1 1 1h18z"/>
                            </svg>
                            Save Identity Settings
                        </button>
                    </div>
                </form>
            </section>

            <section class="ai-admin-section">
                <div class="ai-section-header">
                    <div class="ai-section-title">
                        <h2>
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <path d="M12 6v6l4 2"/>
                            </svg>
                            Quick Start Guide
                        </h2>
                        <p>Get started with your AI image tool in just a few steps</p>
                    </div>
                </div>
                
                <div class="ai-guide-content">
                    <div class="ai-guide-steps">
                        <div class="ai-guide-step">
                            <div class="ai-step-number">1</div>
                            <div class="ai-step-content">
                                <h3>Configure API</h3>
                                <p>Add your Replicate API token to enable AI image processing</p>
                            </div>
                        </div>
                        
                        <div class="ai-guide-step">
                            <div class="ai-step-number">2</div>
                            <div class="ai-step-content">
                                <h3>Upload Overlays</h3>
                                <p>Add architectural overlay images (PNG with transparency)</p>
                            </div>
                        </div>
                        
                        <div class="ai-guide-step">
                            <div class="ai-step-number">3</div>
                            <div class="ai-step-content">
                                <h3>Customize Brand</h3>
                                <p>Set your brand colors and styling preferences</p>
                            </div>
                        </div>
                        
                        <div class="ai-guide-step">
                            <div class="ai-step-number">4</div>
                            <div class="ai-step-content">
                                <h3>Add to Pages</h3>
                                <p>Use the shortcode <code>[ai_image_tool]</code> on any page or post</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div> 