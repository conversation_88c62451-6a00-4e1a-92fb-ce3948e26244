/**
 * AI Styled Image - Modern Admin JavaScript
 */

class AIStyledAdmin {
    constructor() {
        this.modal = null;
        this.mediaFrame = null;
        this.currentEditId = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeComponents();
    }

    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('#add-overlay-btn, .ai-empty-action')) {
                this.openUploadModal();
            } else if (e.target.matches('#media-library-btn')) {
                this.openMediaLibrary();
            } else if (e.target.matches('#close-upload-modal, .ai-modal-overlay')) {
                this.closeUploadModal();
            } else if (e.target.matches('#cancel-upload')) {
                this.closeUploadModal();
            } else if (e.target.matches('#change-image')) {
                this.changeImage();
            } else if (e.target.matches('.ai-delete-overlay')) {
                this.deleteOverlay(e.target.dataset.id);
            } else if (e.target.matches('.ai-edit-overlay')) {
                this.editOverlay(e.target.dataset.id);
            } else if (e.target.matches('.ai-toggle-password')) {
                this.togglePasswordVisibility(e.target.dataset.target);
            }
        });

        const uploadForm = document.getElementById('overlay-upload-form');
        if (uploadForm) {
            uploadForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleUpload();
            });
        }

        const identityForm = document.getElementById('identity-form');
        if (identityForm) {
            identityForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleIdentitySubmit();
            });
        }

        const dropzone = document.getElementById('upload-dropzone');
        if (dropzone) {
            dropzone.addEventListener('click', () => {
                document.getElementById('overlay-file').click();
            });

            dropzone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropzone.classList.add('drag-over');
            });

            dropzone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                dropzone.classList.remove('drag-over');
            });

            dropzone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropzone.classList.remove('drag-over');
                this.handleFileSelect(e.dataTransfer.files);
            });
        }

        const fileInput = document.getElementById('overlay-file');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelect(e.target.files);
            });
        }

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal && this.modal.style.display !== 'none') {
                this.closeUploadModal();
            }
        });
    }

    initializeComponents() {
        this.modal = document.getElementById('upload-modal');
        this.setupTooltips();
        this.validateSettings();
        this.initializeIdentitySettings();
    }

    setupTooltips() {
        const actionButtons = document.querySelectorAll('.ai-action-btn');
        actionButtons.forEach(btn => {
            btn.addEventListener('mouseenter', (e) => {
                const tooltip = document.createElement('div');
                tooltip.className = 'ai-tooltip';
                tooltip.textContent = btn.getAttribute('title');
                tooltip.style.cssText = `
                    position: absolute;
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 0.5rem 0.75rem;
                    border-radius: 0.375rem;
                    font-size: 0.75rem;
                    z-index: 1000;
                    pointer-events: none;
                    transform: translateX(-50%);
                    white-space: nowrap;
                `;
                
                const rect = btn.getBoundingClientRect();
                tooltip.style.left = rect.left + rect.width / 2 + 'px';
                tooltip.style.top = rect.top - 40 + 'px';
                
                document.body.appendChild(tooltip);
                btn._tooltip = tooltip;
            });

            btn.addEventListener('mouseleave', () => {
                if (btn._tooltip) {
                    btn._tooltip.remove();
                    btn._tooltip = null;
                }
            });
        });
    }

    validateSettings() {
        const apiTokenField = document.getElementById('api_token');
        if (apiTokenField) {
            apiTokenField.addEventListener('input', this.debounce(() => {
                this.validateApiToken(apiTokenField.value);
            }, 500));
        }
    }

    validateApiToken(token) {
        const isValid = token.startsWith('r8_') && token.length > 10;
        const field = document.getElementById('api_token');
        
        if (isValid) {
            field.style.borderColor = 'var(--ai-admin-success)';
        } else if (token.length > 0) {
            field.style.borderColor = 'var(--ai-admin-danger)';
        } else {
            field.style.borderColor = 'var(--ai-admin-border)';
        }
    }

    openUploadModal() {
        if (this.modal) {
            this.modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            this.resetForm();
            
            setTimeout(() => {
                const firstInput = this.modal.querySelector('input[type="text"]');
                if (firstInput) firstInput.focus();
            }, 300);
        }
    }

    closeUploadModal() {
        if (this.modal) {
            this.modal.style.display = 'none';
            document.body.style.overflow = '';
            this.resetForm();
            this.currentEditId = null;
        }
    }

    resetForm() {
        const form = document.getElementById('overlay-upload-form');
        if (form) {
            form.reset();
            document.getElementById('image-preview-area').style.display = 'none';
            document.getElementById('file-upload-method').style.display = 'block';
            document.getElementById('media-attachment-id').value = '';
            
            const dropzone = document.getElementById('upload-dropzone');
            if (dropzone) {
                dropzone.classList.remove('drag-over');
            }
        }
    }

    openMediaLibrary() {
        if (this.mediaFrame) {
            this.mediaFrame.open();
            return;
        }

        this.mediaFrame = wp.media({
            title: 'Select Overlay Image',
            button: { text: 'Use This Image' },
            multiple: false,
            library: { type: 'image' }
        });

        this.mediaFrame.on('select', () => {
            const attachment = this.mediaFrame.state().get('selection').first().toJSON();
            
            if (!attachment.mime || !attachment.mime.includes('png')) {
                this.showNotification('Please select a PNG image file.', 'error');
                return;
            }

            this.handleMediaSelection(attachment);
        });

        this.mediaFrame.open();
    }

    handleMediaSelection(attachment) {
        this.openUploadModal();
        
        document.getElementById('media-attachment-id').value = attachment.id;
        
        this.showImagePreview(attachment.url, attachment.filename, attachment.filesizeHumanReadable);
        
        const titleField = document.getElementById('overlay-title');
        if (titleField && !titleField.value) {
            titleField.value = attachment.title || attachment.filename.replace(/\.[^/.]+$/, "");
        }
    }

    handleFileSelect(files) {
        if (files.length === 0) return;

        const file = files[0];
        
        if (!file.type.includes('png')) {
            this.showNotification('Please select a PNG image file.', 'error');
            return;
        }

        const maxSize = parseInt(document.querySelector('input[name="max_file_size"]')?.value || '10485760');
        if (file.size > maxSize) {
            this.showNotification(`File size must be less than ${this.formatFileSize(maxSize)}.`, 'error');
            return;
        }

        const fileInput = document.getElementById('overlay-file');
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        fileInput.files = dataTransfer.files;

        const reader = new FileReader();
        reader.onload = (e) => {
            this.showImagePreview(e.target.result, file.name, this.formatFileSize(file.size));
        };
        reader.readAsDataURL(file);

        const titleField = document.getElementById('overlay-title');
        if (titleField && !titleField.value) {
            titleField.value = file.name.replace(/\.[^/.]+$/, "");
        }
    }

    showImagePreview(src, name, size) {
        const previewArea = document.getElementById('image-preview-area');
        const previewImg = document.getElementById('preview-img');
        const previewName = document.getElementById('preview-name');
        const previewSize = document.getElementById('preview-size');
        
        previewImg.src = src;
        previewName.textContent = name;
        previewSize.textContent = size;
        
        document.getElementById('file-upload-method').style.display = 'none';
        previewArea.style.display = 'flex';
    }

    changeImage() {
        document.getElementById('image-preview-area').style.display = 'none';
        document.getElementById('file-upload-method').style.display = 'block';
        document.getElementById('overlay-file').value = '';
        document.getElementById('media-attachment-id').value = '';
    }

    async handleUpload() {
        const form = document.getElementById('overlay-upload-form');
        const submitBtn = document.getElementById('upload-overlay-btn');
        const formData = new FormData(form);
        
        formData.append('action', 'ai_styled_upload_overlay');
        formData.append('nonce', aiStyledAdmin.nonce);
        
        if (this.currentEditId) {
            formData.append('edit_id', this.currentEditId);
        }

        const title = formData.get('title');
        const hasFile = formData.get('overlay_image').size > 0;
        const hasMediaId = formData.get('media_id');

        if (!title.trim()) {
            this.showNotification('Please enter a title for the overlay.', 'error');
            return;
        }

        if (!hasFile && !hasMediaId && !this.currentEditId) {
            this.showNotification('Please select an image file.', 'error');
            return;
        }

        submitBtn.classList.add('loading');
        submitBtn.disabled = true;

        try {
            const response = await fetch(ajaxurl, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Overlay saved successfully!', 'success');
                this.closeUploadModal();
                this.refreshOverlayGrid();
            } else {
                this.showNotification(result.data || 'Upload failed. Please try again.', 'error');
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showNotification('Upload failed. Please check your connection and try again.', 'error');
        } finally {
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
        }
    }

    async deleteOverlay(id) {
        if (!confirm('Are you sure you want to delete this overlay? This action cannot be undone.')) {
            return;
        }

        const overlayCard = document.querySelector(`[data-id="${id}"]`);
        if (overlayCard) {
            overlayCard.style.opacity = '0.5';
            overlayCard.style.pointerEvents = 'none';
        }

        try {
            const response = await fetch(ajaxurl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'ai_styled_delete_overlay',
                    nonce: aiStyledAdmin.nonce,
                    id: id
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Overlay deleted successfully.', 'success');
                if (overlayCard) {
                    overlayCard.remove();
                }
                this.updateStats();
            } else {
                this.showNotification(result.data || 'Delete failed. Please try again.', 'error');
                if (overlayCard) {
                    overlayCard.style.opacity = '';
                    overlayCard.style.pointerEvents = '';
                }
            }
        } catch (error) {
            console.error('Delete error:', error);
            this.showNotification('Delete failed. Please check your connection and try again.', 'error');
            if (overlayCard) {
                overlayCard.style.opacity = '';
                overlayCard.style.pointerEvents = '';
            }
        }
    }

    async editOverlay(id) {
        try {
            const response = await fetch(ajaxurl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'ai_styled_get_overlay',
                    nonce: aiStyledAdmin.nonce,
                    id: id
                })
            });

            const result = await response.json();

            if (result.success) {
                this.currentEditId = id;
                this.populateEditForm(result.data);
                this.openUploadModal();
            } else {
                this.showNotification(result.data || 'Failed to load overlay data.', 'error');
            }
        } catch (error) {
            console.error('Edit error:', error);
            this.showNotification('Failed to load overlay data. Please try again.', 'error');
        }
    }

    populateEditForm(overlay) {
        document.getElementById('overlay-title').value = overlay.title || '';
        document.getElementById('overlay-category').value = overlay.category || 'general';
        document.getElementById('overlay-description').value = overlay.description || '';
        document.getElementById('overlay-prompt').value = overlay.prompt_template || '';
        
        if (overlay.image_url) {
            this.showImagePreview(overlay.image_url, overlay.title, 'Current image');
        }
        
        const modalTitle = document.querySelector('.ai-modal-header h3');
        if (modalTitle) {
            modalTitle.textContent = 'Edit Overlay';
        }
        
        const submitBtn = document.getElementById('upload-overlay-btn');
        if (submitBtn) {
            submitBtn.innerHTML = `
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="m9 12 2 2 4-4"/>
                    <path d="M21 12c.552 0 1-.448 1-1V5a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v6c0 .552.448 1 1 1h18z"/>
                </svg>
                Update Overlay
            `;
        }
    }

    async refreshOverlayGrid() {
        const grid = document.querySelector('.ai-overlays-grid');
        if (!grid) return;

        try {
            const response = await fetch(ajaxurl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'ai_styled_get_overlays',
                    nonce: aiStyledAdmin.nonce
                })
            });

            const result = await response.json();

            if (result.success) {
                grid.innerHTML = result.data;
                this.updateStats();
            }
        } catch (error) {
            console.error('Refresh error:', error);
        }
    }

    updateStats() {
        const overlayCount = document.querySelectorAll('.ai-overlay-card').length;
        const countElement = document.querySelector('.ai-stat-card .ai-stat-number');
        if (countElement) {
            countElement.textContent = overlayCount;
        }
    }

    togglePasswordVisibility(targetId) {
        const field = document.getElementById(targetId);
        const button = document.querySelector(`[data-target="${targetId}"]`);
        
        if (field && button) {
            const isPassword = field.type === 'password';
            field.type = isPassword ? 'text' : 'password';
            
            const icon = button.querySelector('svg');
            if (icon) {
                icon.innerHTML = isPassword 
                    ? '<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/><line x1="1" y1="1" x2="23" y2="23"/>'
                    : '<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/><circle cx="12" cy="12" r="3"/>';
            }
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `ai-notification ai-notification-${type}`;
        notification.innerHTML = `
            <div class="ai-notification-content">
                <div class="ai-notification-icon">
                    ${this.getNotificationIcon(type)}
                </div>
                <span class="ai-notification-message">${message}</span>
                <button type="button" class="ai-notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        if (!document.querySelector('#ai-admin-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'ai-admin-notification-styles';
            style.textContent = `
                .ai-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    max-width: 400px;
                    border-radius: var(--ai-admin-radius);
                    box-shadow: var(--ai-admin-shadow-lg);
                    animation: slideIn 0.3s ease-out;
                }
                
                .ai-notification-info {
                    background: var(--ai-admin-primary);
                    color: var(--ai-admin-surface);
                }
                
                .ai-notification-success {
                    background: var(--ai-admin-success);
                    color: var(--ai-admin-surface);
                }
                
                .ai-notification-error {
                    background: var(--ai-admin-danger);
                    color: var(--ai-admin-surface);
                }
                
                .ai-notification-content {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    padding: 12px 16px;
                }
                
                .ai-notification-icon {
                    flex-shrink: 0;
                    width: 20px;
                    height: 20px;
                }
                
                .ai-notification-message {
                    flex: 1;
                    font-size: 14px;
                    font-weight: 500;
                }
                
                .ai-notification-close {
                    background: none;
                    border: none;
                    color: inherit;
                    font-size: 18px;
                    font-weight: 700;
                    cursor: pointer;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 4px;
                    opacity: 0.8;
                    flex-shrink: 0;
                }
                
                .ai-notification-close:hover {
                    opacity: 1;
                    background: rgba(255, 255, 255, 0.1);
                }
                
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    getNotificationIcon(type) {
        const icons = {
            info: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"/><path d="m9,12l2,2 4,-4"/></svg>',
            success: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m9,12l2,2 4,-4"/><circle cx="12" cy="12" r="10"/></svg>',
            error: '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"/><line x1="15" y1="9" x2="9" y2="15"/><line x1="9" y1="9" x2="15" y2="15"/></svg>'
        };
        return icons[type] || icons.info;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    initializeIdentitySettings() {
        const colorInputs = document.querySelectorAll('.ai-color-input');
        colorInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                this.updateColorDisplay(e.target);
            });
        });
    }

    updateColorDisplay(colorInput) {
        const wrapper = colorInput.closest('.ai-color-input-wrapper');
        if (wrapper) {
            const valueSpan = wrapper.querySelector('.ai-color-value');
            if (valueSpan) {
                valueSpan.textContent = colorInput.value.toUpperCase();
            }
        }
    }

    async handleIdentitySubmit() {
        const form = document.getElementById('identity-form');
        const submitBtn = form.querySelector('button[type="submit"]');
        const formData = new FormData(form);
        
        const identitySettings = {
            brand_name: formData.get('brand_name'),
            font_family: formData.get('font_family'),
            primary_color: formData.get('primary_color'),
            secondary_color: formData.get('secondary_color'),
            accent_color: formData.get('accent_color'),
            background_color: formData.get('background_color'),
            surface_color: formData.get('surface_color'),
            text_color: formData.get('text_color'),
            muted_color: formData.get('muted_color'),
            border_color: formData.get('border_color'),
            border_radius: formData.get('border_radius'),
            form_style: formData.get('form_style')
        };

        submitBtn.classList.add('loading');
        submitBtn.disabled = true;

        try {
            const response = await fetch(ajaxurl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'ai_styled_save_identity',
                    nonce: aiStyledAdmin.nonce,
                    identity_settings: JSON.stringify(identitySettings)
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Identity settings saved successfully!', 'success');
            } else {
                this.showNotification(result.data || 'Failed to save settings. Please try again.', 'error');
            }
        } catch (error) {
            console.error('Identity save error:', error);
            this.showNotification('Failed to save settings. Please check your connection and try again.', 'error');
        } finally {
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
        }
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new AIStyledAdmin();
}); 